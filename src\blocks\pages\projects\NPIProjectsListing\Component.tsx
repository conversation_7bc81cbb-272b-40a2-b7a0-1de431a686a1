'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardDescription,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { X, MapPin, Calendar, Users, DollarSign, Target, Handshake } from 'lucide-react'

interface Project {
  id: string
  title: string
  description: string
  category: string
  pillar: string
  location: string
  duration: string
  status: 'active' | 'completed' | 'upcoming'
  participants: number
  budget: string
  objectives: string[]
  partners: string[]
  image: string
}

interface NPIProjectsListingProps {
  title?: string
  description?: string
  projects?: Project[]
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-[#25718A] text-white'
    case 'completed':
      return 'bg-[#8A3E25] text-white'
    case 'upcoming':
      return 'bg-[#725242] text-white'
    default:
      return 'bg-[#725242] text-white'
  }
}

const getCategoryColor = (category: string) => {
  const colors = [
    { bg: 'bg-[#8A3E25]', text: 'text-white', border: 'border-[#8A3E25]' },
    { bg: 'bg-[#25718A]', text: 'text-white', border: 'border-[#25718A]' },
    { bg: 'bg-[#725242]', text: 'text-white', border: 'border-[#725242]' },
    { bg: 'bg-[#EFE3BA]', text: 'text-black', border: 'border-[#8A3E25]' },
    { bg: 'bg-[#FFFFFF]', text: 'text-black', border: 'border-[#25718A]' },
  ]
  const index = category.length % colors.length
  return colors[index]
}

export const NPIProjectsListingBlock: React.FC<NPIProjectsListingProps> = ({
  title = 'Our Projects & Initiatives',
  description = "Comprehensive projects transforming Kenya's natural products landscape through community-driven innovation, capacity building, and sustainable development.",
  projects = [
    {
      id: 'knowledge-documentation',
      title: 'Indigenous Knowledge Documentation Project',
      description:
        "Systematic documentation of traditional knowledge systems across Kenya's 47 counties, creating a comprehensive digital repository for preservation and access.",
      category: 'Knowledge Preservation',
      pillar: 'Indigenous Knowledge Documentation',
      location: 'All 47 Counties',
      duration: '2022-2025',
      status: 'active',
      participants: 2500,
      budget: 'KES 150M',
      objectives: [
        'Document traditional medicinal practices',
        'Create digital knowledge repository',
        'Train community knowledge keepers',
        'Establish preservation protocols',
      ],
      partners: ['University of Nairobi', 'Kenya Medical Research Institute', 'County Governments'],
      image: '/assets/product 1.jpg',
    },
    {
      id: 'community-empowerment',
      title: 'Community-Based Natural Products Development',
      description:
        'Empowering local communities to develop sustainable natural product enterprises through training, capacity building, and market linkage support.',
      category: 'Community Development',
      pillar: 'Capacity Building',
      location: 'Central & Eastern Kenya',
      duration: '2023-2026',
      status: 'active',
      participants: 1800,
      budget: 'KES 120M',
      objectives: [
        'Establish community enterprises',
        'Provide technical training',
        'Create market linkages',
        'Build processing facilities',
      ],
      partners: ['Kenya Association of Manufacturers', 'USAID', 'Local CBOs'],
      image: '/assets/product 2.jpg',
    },
    {
      id: 'innovation-hub',
      title: 'Natural Products Innovation Hub',
      description:
        'State-of-the-art research and development facility for natural products innovation, product testing, and commercialization support.',
      category: 'Innovation & Research',
      pillar: 'Product Development',
      location: 'Nairobi',
      duration: '2024-2027',
      status: 'upcoming',
      participants: 500,
      budget: 'KES 200M',
      objectives: [
        'Establish R&D facilities',
        'Support product development',
        'Provide testing services',
        'Facilitate commercialization',
      ],
      partners: [
        'Kenya Bureau of Standards',
        'International Development Partners',
        'Private Sector',
      ],
      image: '/assets/product 3.jpg',
    },
    {
      id: 'market-development',
      title: 'Natural Products Market Development Initiative',
      description:
        'Comprehensive market development program to create sustainable value chains and expand market access for natural products.',
      category: 'Market Development',
      pillar: 'Value Chain Development',
      location: 'Nationwide',
      duration: '2023-2025',
      status: 'active',
      participants: 3200,
      budget: 'KES 180M',
      objectives: [
        'Develop value chains',
        'Create market platforms',
        'Establish quality standards',
        'Build export capacity',
      ],
      partners: ['Export Promotion Council', 'Kenya Trade Network Agency', 'Regional Buyers'],
      image: '/assets/product 4.jpg',
    },
    {
      id: 'youth-engagement',
      title: 'Youth in Natural Products Initiative',
      description:
        'Engaging young entrepreneurs in natural products development through mentorship, funding, and business development support.',
      category: 'Youth Development',
      pillar: 'Capacity Building',
      location: 'Urban Centers',
      duration: '2024-2026',
      status: 'upcoming',
      participants: 1000,
      budget: 'KES 80M',
      objectives: [
        'Train young entrepreneurs',
        'Provide startup funding',
        'Offer mentorship programs',
        'Create youth networks',
      ],
      partners: ['Kenya Youth Business Trust', 'Universities', 'Youth Organizations'],
      image: '/assets/product 5.jpg',
    },
    {
      id: 'research-collaboration',
      title: 'International Research Collaboration Program',
      description:
        'Building partnerships with international research institutions to advance natural products research and development.',
      category: 'Research Collaboration',
      pillar: 'Knowledge Documentation',
      location: 'Multiple Countries',
      duration: '2022-2024',
      status: 'completed',
      participants: 200,
      budget: 'KES 60M',
      objectives: [
        'Establish research partnerships',
        'Exchange knowledge and expertise',
        'Develop joint research projects',
        'Build institutional capacity',
      ],
      partners: ['International Universities', 'Research Institutes', 'Development Partners'],
      image: '/assets/product 6.jpg',
    },
    {
      id: 'policy-framework',
      title: 'Natural Products Policy Framework Development',
      description:
        'Developing comprehensive policy frameworks to support the growth and regulation of the natural products industry.',
      category: 'Policy Development',
      pillar: 'Regulatory Framework',
      location: 'National',
      duration: '2023-2024',
      status: 'completed',
      participants: 150,
      budget: 'KES 40M',
      objectives: [
        'Develop policy frameworks',
        'Engage stakeholders',
        'Create regulatory guidelines',
        'Facilitate implementation',
      ],
      partners: ['Ministry of Health', 'Parliament', 'Industry Associations'],
      image: '/assets/product 7.jpg',
    },
    {
      id: 'technology-transfer',
      title: 'Technology Transfer and Commercialization',
      description:
        'Facilitating the transfer of research innovations to commercial applications through partnerships and support programs.',
      category: 'Technology Transfer',
      pillar: 'Product Development',
      location: 'Nairobi & Mombasa',
      duration: '2024-2027',
      status: 'active',
      participants: 800,
      budget: 'KES 100M',
      objectives: [
        'Transfer research to market',
        'Support commercialization',
        'Build industry partnerships',
        'Create innovation ecosystem',
      ],
      partners: ['Technology Companies', 'Research Institutions', 'Venture Capital'],
      image: '/assets/product 8.jpg',
    },
  ],
}) => {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [filter, setFilter] = useState<string>('all')

  const categories = ['all', ...Array.from(new Set(projects.map((p) => p.category)))]
  const filteredProjects =
    filter === 'all' ? projects : projects.filter((p) => p.category === filter)

  return (
    <NPISection className="py-24 bg-[#FFFFFF]">
      <div className="container mx-auto px-4">
        <NPISectionHeader className="text-center mb-16">
          <NPISectionTitle className="text-black mb-4">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#725242] max-w-3xl mx-auto">
            {description}
          </NPISectionDescription>
        </NPISectionHeader>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <NPIButton
              key={category}
              variant={filter === category ? 'primary' : 'outline'}
              onClick={() => setFilter(category)}
              className={`${
                filter === category
                  ? 'bg-[#8A3E25] text-white border-[#8A3E25]'
                  : 'border-[#25718A] text-[#25718A] hover:bg-[#25718A] hover:text-white'
              } transition-all duration-300`}
            >
              {category === 'all' ? 'All Projects' : category}
            </NPIButton>
          ))}
        </div>

        {/* Projects Grid - Square cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ scale: 1.05, y: -10 }}
              className="aspect-square"
            >
              <NPICard className="h-full w-full overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 border-transparent hover:border-[#725242]/30 flex flex-col">
                {/* Square Image Section - Takes up top 40% */}
                <div className="relative h-[40%] w-full flex-shrink-0">
                  <Image src={project.image} alt={project.title} fill className="object-cover" />
                  <div className="absolute top-2 left-2">
                    <span
                      className={`px-2 py-1 text-xs font-medium ${getStatusColor(project.status)}`}
                    >
                      {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                    </span>
                  </div>
                  <div className="absolute top-2 right-2">
                    <span
                      className={`px-2 py-1 text-xs font-bold ${getCategoryColor(project.category).bg} ${getCategoryColor(project.category).text} border ${getCategoryColor(project.category).border}`}
                    >
                      {project.category}
                    </span>
                  </div>
                </div>

                {/* Content Section - Takes up remaining 60% */}
                <div className="h-[60%] p-4 flex flex-col justify-between">
                  {/* Title and Description */}
                  <div className="flex-1">
                    <h3 className="text-black text-sm font-bold mb-2 line-clamp-2 leading-tight">
                      {project.title}
                    </h3>
                    <p className="text-[#725242] text-xs line-clamp-2 mb-3 leading-relaxed">
                      {project.description}
                    </p>
                  </div>

                  {/* Key Details - Compact */}
                  <div className="space-y-1 mb-3">
                    <div className="flex items-center gap-2 text-xs text-[#725242]">
                      <MapPin className="w-3 h-3 text-[#8A3E25] flex-shrink-0" />
                      <span className="truncate">{project.location}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-[#725242]">
                      <Users className="w-3 h-3 text-[#8A3E25] flex-shrink-0" />
                      <span className="truncate">{project.participants.toLocaleString()} participants</span>
                    </div>
                  </div>

                  {/* Button */}
                  <NPIButton
                    onClick={() => setSelectedProject(project)}
                    className="w-full bg-[#8A3E25] hover:bg-[#25718A] text-white font-bold transition-all duration-300 border-2 border-[#8A3E25] hover:border-[#25718A] text-xs py-2"
                  >
                    View Details
                  </NPIButton>
                </div>
              </NPICard>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              className="bg-[#FFFFFF] max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="relative h-64 w-full">
                <Image
                  src={selectedProject.image}
                  alt={selectedProject.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/60" />
                <button
                  onClick={() => setSelectedProject(null)}
                  className="absolute top-4 right-4 bg-[#8A3E25] hover:bg-[#25718A] text-white p-2 transition-colors border border-white/20"
                >
                  <X className="w-6 h-6" />
                </button>
                <div className="absolute bottom-6 left-6 text-white">
                  <h2 className="text-2xl md:text-3xl font-bold mb-2">{selectedProject.title}</h2>
                  <div className="flex items-center gap-4">
                    <span
                      className={`px-3 py-1 text-sm font-medium ${getStatusColor(selectedProject.status)}`}
                    >
                      {selectedProject.status.charAt(0).toUpperCase() +
                        selectedProject.status.slice(1)}
                    </span>
                    <span
                      className={`px-3 py-1 text-sm font-bold ${getCategoryColor(selectedProject.category).bg} ${getCategoryColor(selectedProject.category).text} border ${getCategoryColor(selectedProject.category).border}`}
                    >
                      {selectedProject.category}
                    </span>
                  </div>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-8">
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Left Column */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold text-black mb-3">Project Overview</h3>
                      <p className="text-[#725242] leading-relaxed">
                        {selectedProject.description}
                      </p>
                    </div>

                    <div>
                      <h3 className="text-xl font-bold text-black mb-3">Key Details</h3>
                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <MapPin className="w-5 h-5 text-[#8A3E25]" />
                          <span className="text-[#725242]">
                            <strong>Location:</strong> {selectedProject.location}
                          </span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Calendar className="w-5 h-5 text-[#8A3E25]" />
                          <span className="text-[#725242]">
                            <strong>Duration:</strong> {selectedProject.duration}
                          </span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Users className="w-5 h-5 text-[#8A3E25]" />
                          <span className="text-[#725242]">
                            <strong>Participants:</strong>{' '}
                            {selectedProject.participants.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-3">
                          <DollarSign className="w-5 h-5 text-[#8A3E25]" />
                          <span className="text-[#725242]">
                            <strong>Budget:</strong> {selectedProject.budget}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold text-black mb-3 flex items-center gap-2">
                        <Target className="w-5 h-5 text-[#8A3E25]" />
                        Key Objectives
                      </h3>
                      <ul className="space-y-2">
                        {selectedProject.objectives.map((objective, index) => (
                          <li key={index} className="flex items-start gap-2 text-[#725242]">
                            <span className="w-2 h-2 bg-[#8A3E25] mt-2 flex-shrink-0"></span>
                            <span>{objective}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-xl font-bold text-black mb-3 flex items-center gap-2">
                        <Handshake className="w-5 h-5 text-[#8A3E25]" />
                        Key Partners
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedProject.partners.map((partner, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-gradient-to-r from-[#725242] to-[#8A3E25] text-[#FFFFFF] text-sm font-medium"
                          >
                            {partner}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="pt-4">
                      <NPIButton
                        className="w-full bg-gradient-to-r from-[#8A3E25] to-[#725242] hover:from-[#725242] hover:to-[#8A3E25] text-[#FFFFFF] font-bold py-3"
                        onClick={() => setSelectedProject(null)}
                      >
                        Close Details
                      </NPIButton>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </NPISection>
  )
}
