import type { Block } from 'payload'

export const NPIProjectsHero: Block = {
  slug: 'npiProjectsHero',
  interfaceName: 'NPIProjectsHeroBlock',
  fields: [
    {
      name: 'title',
      type: 'text',
      defaultValue: 'Projects & Initiatives',
    },
    {
      name: 'subtitle',
      type: 'text',
      defaultValue: 'Comprehensive projects driving sustainable development through indigenous knowledge preservation, community empowerment, and natural products innovation across Kenya.',
    },
    {
      name: 'totalProjects',
      type: 'number',
      defaultValue: 35,
    },
    {
      name: 'activeProjects',
      type: 'number',
      defaultValue: 28,
    },
    {
      name: 'beneficiaries',
      type: 'number',
      defaultValue: 12000,
    },
  ],
  labels: {
    plural: 'NPI Projects Heroes',
    singular: 'NPI Projects Hero',
  },
}
