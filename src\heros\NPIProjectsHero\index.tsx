'use client'

import React from 'react'
import Link from 'next/link'
import { NPIParallaxHero } from '@/components/ui/npi-parallax-hero'
import { NPIButton } from '@/components/ui/npi-button'
import { motion } from 'framer-motion'
import { Target, Users, Lightbulb, Database, ArrowDown, TrendingUp } from 'lucide-react'

export const NPIProjectsPageHeroComponent: React.FC = () => {
  const projectStats = [
    {
      icon: <Database className="w-5 h-5" />,
      number: '35',
      label: 'Total Projects',
      color: 'text-[#8A3E25]',
    },
    {
      icon: <Target className="w-5 h-5" />,
      number: '28',
      label: 'Active Projects',
      color: 'text-[#25718A]',
    },
    {
      icon: <Users className="w-5 h-5" />,
      number: '12K+',
      label: 'Beneficiaries',
      color: 'text-white',
    },
    {
      icon: <TrendingUp className="w-5 h-5" />,
      number: '47',
      label: 'Counties Covered',
      color: 'text-[#8A3E25]',
    },
  ]

  return (
    <NPIParallaxHero
      backgroundVideo="/assets/AZhkc7L4RdmED9W-0kozFg-AZhkc7L4FR24vnG-HzFjNQ (1).mp4"
      height="large"
      parallaxSpeed={0.3}
      overlayOpacity={0.7}
      className="relative min-h-[85vh] max-h-[95vh] -mt-16 pt-16"
    >
      {/* Custom Layout Container - Absolute positioning for extreme corners */}
      <div className="absolute inset-0 text-white z-20">
        {/* Left Section - Main Title */}
        <motion.div
          className="absolute bottom-0 left-0 pt-20 px-4 sm:px-4 lg:px-8 max-w-2xl"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="max-w-xl text-left">
            <motion.p
              className="text-sm md:text-base text-[#EFE3BA] font-light mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              Natural Products Industry Initiative
            </motion.p>
            <motion.h1
              className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold mb-4 leading-[1.1] tracking-[-0.02em]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Transforming Communities Through{' '}
              <span className="text-[#25718A]">Innovative Projects</span>
            </motion.h1>
            <motion.p
              className="text-base md:text-lg text-[#EFE3BA]/90 font-light leading-relaxed max-w-md mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              Comprehensive initiatives driving sustainable development through indigenous knowledge preservation, community empowerment, and natural products innovation across Kenya.
            </motion.p>
          </div>
        </motion.div>

        {/* Right Section - Statistics and CTAs */}
        <motion.div
          className="absolute top-0 right-0 pb-20 px-4 sm:px-6 lg:px-8 max-w-2xl ml-auto"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="max-w-lg space-y-6 text-right">
            {/* Key Statistics */}
            <motion.div
              className="grid grid-cols-2 gap-4 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
            >
              {projectStats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  className="bg-[#725242]/30 backdrop-blur-md border border-[#8A3E25]/40 p-4 text-center hover:bg-[#725242]/40 transition-all duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 + index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -2 }}
                >
                  <div className="flex items-center justify-center mb-2">
                    <div className={stat.color}>{stat.icon}</div>
                  </div>
                  <div className="text-xl font-bold text-white mb-1 font-npi">{stat.number}</div>
                  <div className="text-[#EFE3BA] text-xs font-npi">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-end"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.4 }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-2 border-[#25718A] bg-[#25718A]/20 text-white hover:bg-[#25718A] hover:text-white backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold"
                >
                  <Link href="#projects-listing" className="flex items-center gap-2">
                    <ArrowDown className="w-5 h-5" />
                    Explore Projects
                  </Link>
                </NPIButton>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="primary"
                  className="bg-[#8A3E25] hover:bg-[#25718A] text-white font-bold px-8 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-[#8A3E25] hover:border-[#25718A]"
                >
                  <Link href="/partnerships" className="flex items-center gap-2">
                    Partner With Us
                    <motion.span
                      animate={{ rotate: [0, 10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                    >
                      🤝
                    </motion.span>
                  </Link>
                </NPIButton>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </NPIParallaxHero>
  )
}
