'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { NPIParallaxHero } from '@/components/ui/npi-parallax-hero'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { Target, Users, Lightbulb, Database, ArrowDown, FileText } from 'lucide-react'

interface NPIProgramsHeroProps {
  title?: string
  subtitle?: string
  totalPrograms?: number
  activePrograms?: number
  beneficiaries?: number
}

export const NPIProgramsHeroBlock: React.FC<NPIProgramsHeroProps> = ({
  title = 'Programs & Initiatives',
  subtitle = 'Comprehensive programs driving sustainable development through indigenous knowledge preservation, community empowerment, and natural products innovation across Kenya.',
  totalPrograms = 25,
  activePrograms = 18,
  beneficiaries = 5000,
}) => {
  return (
    <NPIParallaxHero
      backgroundVideo="/assets/AZhkc7L4RdmED9W-0kozFg-AZhkc7L4FR24vnG-HzFjNQ (1).mp4"
      height="large"
      parallaxSpeed={0.3}
      overlayOpacity={0.7}
      className="relative min-h-[95vh] max-h-[95vh] overflow-hidden -mt-16 pt-16"
    >
      {/* Custom Layout Container - Absolute positioning for extreme corners */}
      <div className="absolute inset-0 text-white z-20">
        {/* Top Left Section - Aligned with navbar container */}
        <motion.div
          className="absolute top-0 left-0 pt-26 px-4 sm:px-6 lg:px-25 max-w-7xl"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Organization info */}
            <motion.p
              className="text-sm md:text-base text-[#EFE3BA] font-light"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Natural Products Industry Initiative
            </motion.p>

            {/* Main title */}
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight font-npi"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              {title}
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              className="text-lg md:text-xl text-[#EFE3BA]/90 font-light leading-relaxed max-w-md"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              {subtitle}
            </motion.p>
          </div>
        </motion.div>

        {/* Bottom Right Section - Statistics and Actions */}
        <motion.div
          className="absolute bottom-0 right-0 pb-6 px-6 sm:px-6 lg:px-20 max-w-7xl"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Key Statistics */}
            <motion.div
              className="grid grid-cols-3 gap-4 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            >
              <div className="bg-[#725242]/30 backdrop-blur-md border border-[#8A3E25]/40 p-4 text-center">
                <div className="text-2xl font-bold text-white mb-1 font-npi">{totalPrograms}</div>
                <div className="text-[#EFE3BA] text-sm font-npi">Total Programs</div>
              </div>
              <div className="bg-[#725242]/30 backdrop-blur-md border border-[#8A3E25]/40 p-4 text-center">
                <div className="text-2xl font-bold text-white mb-1 font-npi">{activePrograms}</div>
                <div className="text-[#EFE3BA] text-sm font-npi">Active Programs</div>
              </div>
              <div className="bg-[#725242]/30 backdrop-blur-md border border-[#8A3E25]/40 p-4 text-center">
                <div className="text-2xl font-bold text-white mb-1 font-npi">{beneficiaries.toLocaleString()}+</div>
                <div className="text-[#EFE3BA] text-sm font-npi">Beneficiaries</div>
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.4 }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-2 border-[#25718A] bg-[#25718A]/20 text-white hover:bg-[#25718A] hover:text-white backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold"
                >
                  <Link href="#programs-listing" className="flex items-center gap-2">
                    <ArrowDown className="w-5 h-5" />
                    Explore Programs
                  </Link>
                </NPIButton>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  className="bg-[#8A3E25] hover:bg-[#25718A] text-white backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold border-2 border-[#8A3E25] hover:border-[#25718A]"
                >
                  <Link href="/get-involved" className="flex items-center gap-2">
                    <Users className="w-5 h-5 mr-2" />
                    Get Involved
                  </Link>
                </NPIButton>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </NPIParallaxHero>
  )
}
