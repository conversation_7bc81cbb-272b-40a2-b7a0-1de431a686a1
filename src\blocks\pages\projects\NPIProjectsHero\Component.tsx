'use client'

import React from 'react'
import Link from 'next/link'
import { NPIParallaxHero } from '@/components/ui/npi-parallax-hero'
import { NPIButton } from '@/components/ui/npi-button'
import { motion } from 'framer-motion'
import { Database, Lightbulb, Users, TrendingUp } from 'lucide-react'

interface NPIProjectsHeroProps {
  title?: string
  subtitle?: string
  totalProjects?: number
  activeProjects?: number
  beneficiaries?: number
}

export const NPIProjectsHeroBlock: React.FC<NPIProjectsHeroProps> = ({
  title = 'Projects & Initiatives',
  subtitle = 'Comprehensive projects driving sustainable development through indigenous knowledge preservation, community empowerment, and natural products innovation across Kenya.',
  totalProjects = 35,
  activeProjects = 28,
  beneficiaries = 12000,
}) => {
  const projectTypes = [
    {
      icon: <Database className="w-6 h-6" />,
      title: 'Knowledge Documentation',
      count: '12 Projects',
      description: 'Preserving traditional wisdom',
      bgColor: 'bg-[#EFE3BA]',
      borderColor: 'border-[#8A3E25]',
      iconColor: 'text-[#8A3E25]',
      titleColor: 'text-black',
      textColor: 'text-[#725242]',
    },
    {
      icon: <Lightbulb className="w-6 h-6" />,
      title: 'Innovation & Development',
      count: '8 Projects',
      description: 'Product commercialization',
      bgColor: 'bg-[#FFFFFF]',
      borderColor: 'border-[#25718A]',
      iconColor: 'text-[#25718A]',
      titleColor: 'text-black',
      textColor: 'text-[#725242]',
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: 'Community Empowerment',
      count: '10 Projects',
      description: 'Capacity building initiatives',
      bgColor: 'bg-[#EFE3BA]',
      borderColor: 'border-[#725242]',
      iconColor: 'text-[#725242]',
      titleColor: 'text-black',
      textColor: 'text-[#725242]',
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: 'Market Development',
      count: '5 Projects',
      description: 'Value chain enhancement',
      bgColor: 'bg-[#FFFFFF]',
      borderColor: 'border-[#8A3E25]',
      iconColor: 'text-[#8A3E25]',
      titleColor: 'text-black',
      textColor: 'text-[#725242]',
    },
  ]

  return (
    <NPIParallaxHero
      backgroundVideo="/assets/hero.mp4"
      height="large"
      parallaxSpeed={0.3}
      overlayOpacity={0.7}
      className="relative min-h-[70vh] max-h-[85vh] -mt-16 pt-16"
    >
      {/* Custom Layout Container - Absolute positioning for extreme corners */}
      <div className="absolute inset-0 text-white z-20">
        {/* Top Left Section - Aligned with navbar container */}
        <motion.div
          className="absolute top-0 left-0 pt-26 px-4 sm:px-6 lg:px-25 max-w-7xl"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="max-w-lg space-y-6">
            {/* Organization info */}
            <motion.p
              className="text-sm md:text-base text-[#EFE3BA]/90 font-light"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              Natural Products Industry Initiative
            </motion.p>

            {/* Main title */}
            <motion.h1
              className="text-3xl md:text-4xl lg:text-5xl font-bold leading-[1.1] tracking-[-0.02em] text-white"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {title}
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              className="text-base md:text-lg text-[#EFE3BA]/80 leading-relaxed max-w-md"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              {subtitle}
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="primary"
                  className="bg-[#8A3E25] hover:bg-[#25718A] text-white font-bold px-8 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 border-2 border-[#8A3E25] hover:border-[#25718A]"
                >
                  <Link href="#projects-listing" className="flex items-center gap-2">
                    View All Projects
                    <motion.span
                      animate={{ x: [0, 3, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                    >
                      →
                    </motion.span>
                  </Link>
                </NPIButton>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <NPIButton
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-2 border-[#25718A] bg-[#EFE3BA]/20 text-white hover:bg-[#25718A] hover:text-white backdrop-blur-md px-8 py-4 transition-all duration-300 shadow-lg hover:shadow-xl font-bold hover:border-[#25718A]"
                >
                  <Link href="/partnerships" className="flex items-center gap-2">
                    Partner With Us
                    <motion.span
                      animate={{ rotate: [0, 10, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                    >
                      🤝
                    </motion.span>
                  </Link>
                </NPIButton>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Bottom Right Section - Project Statistics */}
        <motion.div
          className="absolute bottom-0 right-0 pb-16 px-4 sm:px-6 lg:px-8 max-w-7xl ml-auto"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="max-w-2xl">
            {/* Statistics Cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {projectTypes.map((type, index) => (
                <motion.div
                  key={type.title}
                  className={`${type.bgColor} p-4 backdrop-blur-md border-2 ${type.borderColor} hover:shadow-lg transition-all duration-300`}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 + index * 0.1 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={type.iconColor}>{type.icon}</div>
                    <div className={`text-xs font-bold ${type.titleColor}`}>{type.count}</div>
                  </div>
                  <h3 className={`text-sm font-bold ${type.titleColor} mb-1`}>{type.title}</h3>
                  <p className={`text-xs ${type.textColor}`}>{type.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Key Metrics */}
            <motion.div
              className="text-right space-y-2"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <div className="flex justify-end gap-8">
                <div>
                  <div className="text-2xl md:text-3xl font-bold text-[#8A3E25]">
                    {totalProjects}
                  </div>
                  <div className="text-xs text-[#EFE3BA]/80">Total Projects</div>
                </div>
                <div>
                  <div className="text-2xl md:text-3xl font-bold text-[#25718A]">
                    {activeProjects}
                  </div>
                  <div className="text-xs text-[#EFE3BA]/80">Active Projects</div>
                </div>
                <div>
                  <div className="text-2xl md:text-3xl font-bold text-white">
                    {beneficiaries.toLocaleString()}+
                  </div>
                  <div className="text-xs text-[#EFE3BA]/80">Beneficiaries</div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </NPIParallaxHero>
  )
}
